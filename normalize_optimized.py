# ==================== 配置区 ====================
CONFIG = {
    'mode': 'normalize',  # 'tokenize' 或 'normalize'
    'latex_string': r'F _ { { \mathbb T } _ { c } ^ { 2 } } \sim \frac { 1 } { \mathrm { v o l } ( { \mathbb T } _ { c } ^ { 2 } ) _ { h e t } } \, , ',
    'remove_trailing': False,
    'enable_synonym_replacement': False,
    'unify_environments': False,
}

# ==================== 配置区结束 ====================

import os
import re
import subprocess
import tempfile
import csv
from pathlib import Path

# ==================== 常量定义 ====================

# Token类型定义 (从原脚本复制)
SKIP_PATTERNS = [r'\{', r'\}', r'[\[\]]', r'\\begin\{.*?\}', r'\\end\{.*?\}', r'\^', r'\_', r'\\.*rule.*', r'\\.*line.*', r'\[[\-.0-9]+[epm][xtm]\]']
SKIP_Tokens = ['\\', '\\\\', '\\index', '\\a', '&', '$', '\\multirow', '\\def', '\\edef', '\\raggedright', '\\url', '\\cr', '\\ensuremath', '\\left', '\\right',
               '\\mathchoice', '\\scriptstyle', '\\displaystyle', '\\qquad', '\\quad', '\\,', '\\!', '~', '\\boldmath', '\\gdef', '\\today', '\\the']
PHANTOM_Tokens = ['\\fontfamily', '\\vphantom', '\\phantom', '\\rowcolor', '\\ref', '\\thesubequation', '\\global', '\\theboldgroup']
TWO_Tail_Tokens = ['\\frac', '\\binom']
AB_Tail_Tokens = ['\\xrightarrow', '\\xleftarrow', '\\sqrt']        # special token \xxx [] {}
TWO_Tail_Invisb_Tokens = ['\\overset', '\\underset', '\\stackrel']
ONE_Tail_Tokens = ['\\widetilde', '\\overline', '\\hat', '\\widehat', '\\tilde', '\\Tilde', '\\dot', '\\bar', '\\vec', '\\underline', '\\underbrace', '\\check',
                   '\\breve', '\\Bar', '\\Vec', '\\mathring', '\\ddot', '\\Ddot', '\\dddot', '\\ddddot']
ONE_Tail_Invisb_Tokens = ['\\boldsymbol', '\\pmb', '\\textbf', '\\mathrm', '\\mathbf', '\\mathbb', '\\mathcal', '\\textmd', '\\texttt', '\\textnormal',
                          '\\textit', '\\textup', '\\mathop', '\\mathbin', '\\smash', '\\operatorname', '\\textrm', '\\mathfrak', '\\emph',
                          '\\textsf', '\\textsc']

# 删除了对\text命令的特殊保护

# 脚本目录
SCRIPT_DIR = Path(__file__).parent
JS_DIR = SCRIPT_DIR / "js_scripts"

# ==================== 辅助函数 ====================

def find_matching_brace(sequence, start_index, brace=['{', '}']):
    """
    查找匹配的括号位置 (从原脚本复制)
    
    Args:
        sequence: token序列
        start_index: 起始位置
        brace: 括号对，默认为['{', '}']
    
    Returns:
        匹配括号的索引，如果未找到返回-1
    """
    left_brace, right_brace = brace
    depth = 0
    for i, char in enumerate(sequence[start_index:], start=start_index):
        if char == left_brace:
            depth += 1
        elif char == right_brace:
            depth -= 1
            if depth == 0:
                return i
    if depth > 0:
        print(f"Warning! found no matching brace in sequence starting at {start_index}")
    return -1

def merge_tokens_with_pattern(text, pattern, process_func=None, add_trailing_space=False):
    """
    通用的token合并函数 - 抽象重复逻辑
    
    Args:
        text: 输入文本
        pattern: 正则表达式模式
        process_func: 处理函数，默认为移除空格
        add_trailing_space: 是否在处理后添加尾随空格
    
    Returns:
        处理后的文本
    """
    old_tokens = re.findall(pattern, text, re.DOTALL)
    if not old_tokens:
        return text
    
    if process_func is None:
        process_func = lambda x: x.replace(" ", "")
    
    for old_token in old_tokens:
        new_token = process_func(old_token)
        if add_trailing_space and not new_token.endswith(" "):
            new_token += " "
        text = text.replace(old_token, new_token)
    
    return text

def remove_empty_braces(text):
    """
    删除空花括号 {} (从原脚本复制)
    这个函数会删除所有的空花括号，但保留有意义的空格
    修复：在align环境中更加谨慎，避免删除必要的花括号结构
    """
    # 检查是否包含align环境，如果是，使用更保守的处理
    if '\\begin{align' in text:
        # 在align环境中，我们暂时跳过空花括号的删除
        # 让fix_align_environment_braces函数来处理align环境中的花括号问题
        return text
    else:
        # 对于非align环境，使用原来的处理方式
        result = re.sub(r'\{\s*\}', '', text)

        # 多次执行以处理嵌套的空花括号
        prev_result = ""
        while prev_result != result:
            prev_result = result
            result = re.sub(r'\{\s*\}', '', result)

        return result

def remove_redundant_braces(text):
    """
    删除多余的嵌套花括号
    例如: \\sqrt{{3}} → \\sqrt{3}, \\mathrm{{sin}} → \\mathrm{sin}
    """
    result = text

    # 处理LaTeX命令后的双重花括号
    # 匹配模式：\command{{content}} → \command{content}
    latex_commands = [
        'sqrt', 'frac', 'mathrm', 'mathbf', 'mathit', 'mathcal', 'mathbb', 'mathfrak', 'mathsf', 'mathtt',
        'text', 'textbf', 'textit', 'textrm', 'textsf', 'texttt', 'textsc',
        'hat', 'tilde', 'bar', 'vec', 'dot', 'ddot', 'overline', 'underline',
        'sin', 'cos', 'tan', 'sec', 'csc', 'cot', 'sinh', 'cosh', 'tanh',
        'arcsin', 'arccos', 'arctan', 'ln', 'log', 'exp', 'min', 'max'
    ]

    # 使用迭代方法处理多层嵌套
    prev_result = ""
    iterations = 0
    while prev_result != result and iterations < 10:  # 增加迭代次数
        prev_result = result

        # 处理LaTeX命令的双重花括号
        for cmd in latex_commands:
            # 处理 \command{{content}} → \command{content}
            # 使用更宽松的匹配，支持嵌套内容
            pattern = f'\\\\{cmd}\\{{\\{{([^{{}}]*(?:\\{{[^{{}}]*\\}}[^{{}}]*)*)\\}}\\}}'
            replacement = f'\\\\{cmd}{{{r"\1"}}}'
            result = re.sub(pattern, replacement, result)

            # 处理简单情况
            pattern = f'\\\\{cmd}\\{{\\{{([^{{}}]+)\\}}\\}}'
            replacement = f'\\\\{cmd}{{{r"\1"}}}'
            result = re.sub(pattern, replacement, result)

        # 处理一般的双重花括号
        # 匹配 {{content}} 但不破坏必要的嵌套
        result = re.sub(r'\{\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}\}', r'{\1}', result)

        # 处理简单的双重花括号
        result = re.sub(r'\{\{([^{}]+)\}\}', r'{\1}', result)

        iterations += 1

    return result

def fix_align_environment_braces(text):
    """
    修复align环境中的花括号问题 - 简化版本
    
    由于JavaScript已经优化了align环境的处理，此函数主要用于：
    1. 清理剩余的空花括号
    2. 修复括号平衡问题
    3. 处理特殊情况
    """
    # 检查是否包含align环境
    if '\\begin{align' not in text:
        return text

    result = text

    # 第一步：清理剩余的空花括号
    # 删除 \\begin{align*} 后的空花括号
    result = re.sub(r'(\\begin{align\*?})\s*\{\s*\}', r'\1', result)
    
    # 删除 \\end{align*} 前的空花括号
    result = re.sub(r'\{\s*\}\s*(\\end{align\*?})', r'\1', result)
    
    # 删除换行符周围的空花括号
    result = re.sub(r'\}\s*\\\\\s*\{\s*\}', r'\\\\', result)
    result = re.sub(r'\\\\\s*\{\s*\}', r'\\\\', result)
    result = re.sub(r'\{\s*\}\s*\\\\', r'\\\\', result)
    
    # 删除对齐符号 & 周围的空花括号
    result = re.sub(r'&\s*\{\s*\}', r'&', result)
    result = re.sub(r'\{\s*\}\s*&', r'&', result)
    
    # 第二步：修复空上标问题
    # 只处理真正的空上标，不处理有内容的上标
    result = re.sub(r'\^\s*\{\s*\}\s*', r'^{}', result)  # 处理 ^ {} 的情况
    
    # 第三步：修复换行符和对齐符号的连接问题
    result = re.sub(r'\\\\\s*&=', r'\\\\ &=', result)
    
    # 第四步：清理多余的空格
    result = re.sub(r'\s+', ' ', result)
    result = re.sub(r'^\s+|\s+$', '', result)
    
    # 第五步：括号平衡检查
    open_braces = result.count('{')
    close_braces = result.count('}')
    
    if open_braces > close_braces:
        missing_braces = open_braces - close_braces
        align_end_pos = result.rfind('\\end{align')
        if align_end_pos != -1:
            result = result[:align_end_pos] + '}' * missing_braces + result[align_end_pos:]
        else:
            result = result + '}' * missing_braces
    elif close_braces > open_braces:
        missing_braces = close_braces - open_braces
        align_start_pos = result.find('\\begin{align')
        if align_start_pos != -1:
            result = result[:align_start_pos] + '{' * missing_braces + result[align_start_pos:]
        else:
            result = '{' * missing_braces + result

    return result

def remove_trailing_latex(formula):
    """移除LaTeX公式尾部的间距和装饰命令 (从原脚本复制)"""
    pattern = r'(\\(hspace\*?\{[^{}]*?\}|vspace\*?\{[^{}]*?\}|smallskip|medskip|quad|qquad|bigskip|[;,])|\~|\.)*$'
    cleaned_formula = re.sub(pattern, '', formula, count=1)
    return cleaned_formula

# ==================== 核心函数 ====================

def latex_aware_tokenizer(text):
    """
    LaTeX语法感知的tokenizer - 全面解决token分割问题

    使用状态机方法，正确理解LaTeX结构：
    - 命令识别 (\\command)
    - 参数组识别 ({...}, [...])
    - 强制空格命令 (\\ )
    - 数学函数处理
    - 嵌套结构处理
    """
    tokens = []
    i = 0

    while i < len(text):
        # 跳过普通空格
        if text[i] == ' ' and (i == 0 or text[i-1] != '\\'):
            i += 1
            continue

        # 处理LaTeX命令
        if text[i] == '\\':
            token, next_i = _parse_latex_command(text, i)
            if token:
                tokens.append(token)
            i = next_i
            continue

        # 处理花括号组
        if text[i] == '{':
            token, next_i = _parse_brace_group(text, i)
            if token:
                tokens.append(token)
            i = next_i
            continue

        # 处理方括号组
        if text[i] == '[':
            token, next_i = _parse_bracket_group(text, i)
            if token:
                tokens.append(token)
            i = next_i
            continue

        # 处理单独的特殊字符
        if text[i] in '()[]':
            tokens.append(text[i])
            i += 1
            continue

        # 处理普通字符序列
        token, next_i = _parse_normal_sequence(text, i)
        if token:
            tokens.append(token)
        i = next_i

    return [t for t in tokens if t.strip()]

def _parse_latex_command(text, start):
    """解析LaTeX命令"""
    if start >= len(text) or text[start] != '\\':
        return None, start + 1

    i = start + 1

    # 处理转义字符 \{, \}, \[, \], \$, \%, \&, \#, \_
    if i < len(text) and text[i] in '{}[]$%&#_':
        return f'\\{text[i]}', i + 1

    # 处理LaTeX换行符 \\
    if i < len(text) and text[i] == '\\':
        return '\\\\', i + 1

    # 处理LaTeX特殊命令：间距命令、重音符、数学模式、特殊符号
    # 间距命令: \, \: \; \!
    # 双竖线: \|
    # 重音符: \= \^ \. \~ \" \' \`
    # 数学模式: \( \)
    # 特殊符号: \/ \-
    # 添加 \> 到LaTeX特殊命令列表
    if i < len(text) and text[i] in ',:;!|=^.~"\'`()/-<>':
        return f'\\{text[i]}', i + 1

    # 处理强制空格命令 \ (反斜杠+空格)
    if i < len(text) and text[i] == ' ':
        # 检查后面是否跟着运算符或标点符号（扩展列表包含更多符号）
        # 注意：移除了所有LaTeX特殊命令字符，避免错误解析
        # 移除的字符: , ; : ! | = ^ . ~ " ' ` ( ) / -
        if i + 1 < len(text) and text[i + 1] in '+\\<>*_[]{}':
            return f'\\ {text[i + 1]}', i + 2
        else:
            return '\\ ', i + 1

    # 处理普通命令
    command = '\\'
    while i < len(text) and (text[i].isalpha() or text[i] in '*'):
        command += text[i]
        i += 1

    # 特殊处理数学函数命令
    math_commands = ['\\mathrm', '\\mathbf', '\\mathit', '\\mathcal', '\\mathbb', '\\mathfrak', '\\mathsf', '\\mathtt']
    if command in math_commands:
        # 检查后面是否直接跟着字符（不是花括号）
        if i < len(text) and text[i] not in '{[ ':
            # 只取第一个字符作为参数
            param_char = text[i]
            return command, i  # 返回命令，参数字符将在下次迭代中单独处理

    return command, i

def _parse_brace_group(text, start):
    """解析花括号组"""
    if start >= len(text) or text[start] != '{':
        return None, start + 1

    brace_count = 1
    i = start + 1

    while i < len(text) and brace_count > 0:
        if text[i] == '{':
            brace_count += 1
        elif text[i] == '}':
            brace_count -= 1
        i += 1

    if brace_count == 0:
        return text[start:i], i
    else:
        # 不匹配的花括号，返回到下一个空格或结尾
        while i < len(text) and text[i] != ' ':
            i += 1
        return text[start:i], i

def _parse_bracket_group(text, start):
    """解析方括号组"""
    if start >= len(text) or text[start] != '[':
        return None, start + 1

    bracket_count = 1
    i = start + 1

    while i < len(text) and bracket_count > 0:
        if text[i] == '[':
            bracket_count += 1
        elif text[i] == ']':
            bracket_count -= 1
        i += 1

    if bracket_count == 0:
        return text[start:i], i
    else:
        # 不匹配的方括号，返回到下一个空格或结尾
        while i < len(text) and text[i] != ' ':
            i += 1
        return text[start:i], i

def _parse_normal_sequence(text, start):
    """解析普通字符序列"""
    i = start

    # 特殊处理：如果是多位数字，需要检查是否应该分割为单个数字
    if text[i].isdigit():
        # 检查前面是否是需要单个数字参数的命令（如\frac）
        # 简化处理：对于多位数字，只取第一位
        if len(text) > start + 1 and text[start + 1].isdigit():
            # 多位数字，只取第一位
            i = start + 1
        else:
            # 单位数字，正常处理
            while i < len(text) and text[i].isdigit():
                i += 1
    else:
        while i < len(text) and text[i] not in '\\{}[] ' and not text[i].isdigit():
            i += 1

    if i > start:
        return text[start:i], i
    else:
        return None, start + 1

def tokenize_latex(latex_code):
    """
    LaTeX tokenization函数 - JavaScript AST处理
    移除了对\text{}命令的特殊保护

    Args:
        latex_code: 原始LaTeX字符串

    Returns:
        (success: bool, processed_latex: str)
    """
    if not latex_code:
        return False, latex_code

    # 移除了\text保护逻辑，直接处理原始LaTeX代码
    protected_latex = latex_code

    # 创建临时文件，指定UTF-8编码
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as temp_file:
        temp_filename = temp_file.name

        # 环境统一预处理 - 修复：KaTeX兼容性处理
        prepre = protected_latex

        # KaTeX兼容性：将不支持的环境转换为支持的环境
        # 将align*等环境转换为aligned（KaTeX支持的环境）
        # 修复：避免匹配包含嵌套环境的内容，使用更精确的匹配
        prepre = re.sub(r'\\begin{(split|align|alignedat|alignat|eqnarray)\*?}((?:(?!\\begin{|\\end{).)*?)\\end{\1\*?}',
                       r'\\begin{aligned}\2\\end{aligned}', prepre, flags=re.S)

        # 根据配置决定是否进行额外的环境统一
        if CONFIG.get('unify_environments', False):
            # 将smallmatrix统一为matrix
            prepre = re.sub(r'\\begin{(smallmatrix)\*?}(.+?)\\end{\1\*?}',
                           r'\\begin{matrix}\2\\end{matrix}', prepre, flags=re.S)

        temp_file.write(prepre)
    
    try:
        # 调用JavaScript处理器
        js_script = JS_DIR / "preprocess_formula.js"
        if not js_script.exists():
            return True, prepre  # 返回预处理后的结果

        # 构建命令
        if os.name == 'nt':  # Windows
            cmd = f'type "{temp_filename}" | node "{js_script}" normalize'
        else:  # Unix/Linux/Mac
            cmd = f'cat "{temp_filename}" | node "{js_script}" normalize'

        # 执行命令，指定UTF-8编码
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True,
                              timeout=30, encoding='utf-8', errors='replace')

        if result.returncode != 0:
            return True, prepre  # 返回预处理后的结果

        # 处理输出
        output = result.stdout
        if output is None:
            return True, prepre  # 返回预处理后的结果

        output = output.strip()
        if not output:
            return True, prepre  # 返回预处理后的结果

        # 操作符名称规范化
        operators = '|'.join(['arccos', 'arcsin', 'arctan', 'arg', 'cos', 'cosh', 'cot', 'coth', 'csc', 'deg', 'det', 'dim', 'exp', 'gcd', 'hom', 'inf',
                             'injlim', 'ker', 'lg', 'lim', 'liminf', 'limsup', 'ln', 'log', 'max', 'min', 'Pr', 'projlim', 'sec', 'sin', 'sinh', 'sup', 'tan', 'tanh'])
        ops = re.compile(r'\\operatorname {(' + operators + ')}')

        # 替换operatorname为简化形式
        names = ['\\' + x.replace(' ', '') for x in re.findall(ops, output)]
        if names:
            output = re.sub(ops, lambda match: names.pop(0), output)
        output = output.replace(r'\\ \end{array}', r'\end{array}')

        # 移除了\text{}块的恢复逻辑

        # 环境统一后处理：保持aligned环境，不再恢复原始环境类型
        # 注释掉环境恢复逻辑，保持统一后的aligned环境
        # if 'align*' in latex_code:
        #     output = re.sub(r'\\begin{aligned}(.+?)\\end{aligned}', r'\\begin{align*}\1\\end{align*}', output, flags=re.S)
        # elif 'align}' in latex_code:
        #     output = re.sub(r'\\begin{aligned}(.+?)\\end{aligned}', r'\\begin{align}\1\\end{align}', output, flags=re.S)

        return True, output

    except subprocess.TimeoutExpired:
        return True, prepre  # 返回预处理后的结果
    except Exception as e:
        return True, prepre  # 返回预处理后的结果
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_filename)
        except:
            pass

def unified_space_processing(text):
    """
    统一的空格处理函数 - 先清理后添加，避免冲突
    合并原脚本中clean_latex_spaces和add_required_spaces的逻辑
    """



    result = text

    # === 第一阶段：一次性清理所有不必要空格 ===

    # 0. 保护LaTeX换行符和强制空格命令，避免被空格清理规则破坏
    result = result.replace('\\\\', '__LATEX_NEWLINE__')

    # 保护LaTeX间距命令和强制空格命令
    latex_forced_spaces = []
    def protect_forced_space(match):
        latex_forced_spaces.append(match.group(0))
        return f'__LATEX_FORCED_SPACE_{len(latex_forced_spaces)-1}__'

        # 保护 \ 后跟运算符或标点符号的模式 (\ 是反斜杠+空格)
    # 更新：现在tokenizer已经将强制空格作为单独的token处理
    forced_space_pattern = r'\\ ([+\-=<>*/^_,;:.!?])'
    result = re.sub(forced_space_pattern, protect_forced_space, result)

    # 1. 修复转义空格问题 (从原脚本复制)
    result = re.sub(r'\\\s+=', '=', result)
    result = re.sub(r'\\\s+\{', '{', result)
    result = re.sub(r'\\\s+\}', '}', result)
    result = re.sub(r'\\\s+\[', '[', result)
    result = re.sub(r'\\\s+\]', ']', result)
    result = re.sub(r'\\\s+\(', '(', result)
    result = re.sub(r'\\\s+\)', ')', result)
    result = re.sub(r'\\\s+,', ',', result)
    result = re.sub(r'\\\s+\+', '+', result)
    result = re.sub(r'\\\s+-', '-', result)
    result = re.sub(r'\\\s+\*', '*', result)
    result = re.sub(r'\\\s+/', '/', result)
    result = re.sub(r'\\\s+<', '<', result)
    # 修复：不处理LaTeX间距命令 \>，只处理真正的转义空格+大于号
    # 使用负向后查找，确保前面不是LaTeX间距命令
    result = re.sub(r'(?<!\\\s)(?<!\\>)\s+>', '>', result)
    # 修复：保护LaTeX强制空格命令 \ （反斜杠+单个空格）
    # 只处理反斜杠后跟多个空格的情况，不处理单个空格
    result = re.sub(r'(?<!\\)\\\s{2,}([^a-zA-Z\\])', r'\1', result)

    # 现在保护LaTeX间距命令，避免被后续处理破坏
    latex_spacing_commands = [
        r'\\,',      # 小间距
        r'\\:',      # 中间距
        r'\\;',      # 大间距
        r'\\!',      # 负间距
        r'\\ ',      # 强制空格
        r'\\>',      # 大于号间距
        r'\\quad',   # 1em间距
        r'\\qquad',  # 2em间距
        r'\\enspace', # 0.5em间距
        r'\\thinspace', # 小间距
        r'\\medspace',  # 中间距
        r'\\thickspace', # 大间距
        r'\\negthinspace', # 负小间距
        r'\\negmedspace',  # 负中间距
        r'\\negthickspace' # 负大间距
    ]
    
    for spacing_cmd in latex_spacing_commands:
        result = re.sub(spacing_cmd, lambda m: protect_forced_space(m), result)

    # 2. 清理结构性空格
    result = re.sub(r'(?<!\\text)\s*\{\s*', '{', result)  # 保护\text{}
    result = re.sub(r'\s*\}', '}', result)
    result = re.sub(r'\s*\[\s*', '[', result)
    result = re.sub(r'\s*\]', ']', result)
    result = re.sub(r'\s*\(\s*', '(', result)
    result = re.sub(r'\s*\)', ')', result)

    # 3. 清理运算符空格 (现在强制空格命令已被保护，可以安全清理)
    operators = ['+', '-', '=', '<', '>', '*', '/', '^', '_']
    for op in operators:
        result = re.sub(f'\\s*{re.escape(op)}\\s*', op, result)

    # 4. 清理标点符号空格 (现在强制空格命令已被保护，可以安全清理)
    result = re.sub(r'\s*,\s*', ',', result)
    result = re.sub(r'\s*;\s*', ';', result)
    result = re.sub(r'\s*:\s*', ':', result)
    result = re.sub(r'\s*\.\s*', '.', result)

    # 5. 清理LaTeX命令空格
    result = re.sub(r'(\\[A-Za-z]+)\s*\{', r'\1{', result)
    result = re.sub(r'(\\[A-Za-z]+)\s*\[', r'\1[', result)
    result = re.sub(r'(\\[A-Za-z]+)\s*\(', r'\1(', result)

    # 6. 清理数学字体命令内部空格 (不再特殊处理\mathrm{sin}，因为已改为\sin格式)
    # 清理其他数学字体命令
    math_fonts = ['mathbf', 'mathit', 'mathcal', 'mathbb', 'mathfrak', 'mathsf', 'mathtt', 'mathrm']
    for font in math_fonts:
        def clean_math_font_spaces(match):
            content = match.group(1)
            cleaned_content = re.sub(r'\s+', '', content)
            return f'\\{font}{{{cleaned_content}}}'

        result = re.sub(f'\\\\{font}\\s*\\{{\\s*([^}}]+)\\s*\\}}', clean_math_font_spaces, result)

    # === 第二阶段：一次性添加必要空格（在空格清理之前） ===

    # 需要尾随空格的命令列表 (从原脚本复制，但优化处理逻辑)
    space_required_commands = [
        '\\bf', '\\it', '\\rm', '\\sf', '\\tt', '\\sc', '\\em', '\\sl',
        '\\tiny', '\\scriptsize', '\\footnotesize', '\\small',
        '\\normalsize', '\\large', '\\Large', '\\LARGE', '\\huge', '\\Huge',
        '\\displaystyle', '\\textstyle', '\\scriptstyle', '\\scriptscriptstyle',
        '\\quad', '\\qquad', '\\enspace', '\\thinspace',
        '\\negthinspace', '\\medspace', '\\negmedspace',
        '\\thickspace', '\\negthickspace',
        '\\hline', '\\midrule', '\\toprule', '\\bottomrule',
        '\\cline', '\\cr', '\\tabularnewline',
        '\\noindent', '\\indent', '\\newline',
        '\\linebreak', '\\pagebreak', '\\clearpage',
        '\\LaTeX', '\\TeX', '\\BibTeX',
    ]

    # 保护长命令避免被短命令匹配
    protected_commands = [
        '\\scriptscriptstyle', '\\scriptstyle', '\\scriptsize',
        '\\bfseries', '\\itshape', '\\rmfamily', '\\sffamily', '\\ttfamily',
        '\\emph', '\\slshape', '\\upshape',
        '\\upsilon', '\\Upsilon', '\\iota',
        '\\rmfamily', '\\bfseries',
    ]

    # 保护长命令
    protected_placeholders = {}
    for i, protected_cmd in enumerate(protected_commands):
        if protected_cmd in result:
            placeholder = f"__PROTECTED_CMD_{i}__"
            protected_placeholders[placeholder] = protected_cmd
            result = result.replace(protected_cmd, placeholder)

    # 按长度降序排序，处理需要添加空格的命令
    sorted_commands = sorted(space_required_commands, key=len, reverse=True)
    for cmd in sorted_commands:
        escaped_cmd = re.escape(cmd)
        safe_pattern = f'(?<![\\\\a-zA-Z])({escaped_cmd})(?=[a-zA-Z])(?!(?:silon|shape|family|series))'
        result = re.sub(safe_pattern, r'\1 ', result)

    # 恢复被保护的长命令
    for placeholder, original_cmd in protected_placeholders.items():
        result = result.replace(placeholder, original_cmd)

    # 删除了对\mathrm{}的特殊空格处理，因为数学函数已改为\sin格式

    # 为LaTeX命令和符号之间添加分隔空格 (从原脚本复制逻辑)
    latex_commands_needing_separation = [
        r'\\Delta', r'\\Gamma', r'\\Lambda', r'\\Omega', r'\\Phi', r'\\Pi', r'\\Psi', r'\\Sigma', r'\\Theta', r'\\Upsilon', r'\\Xi',
        r'\\alpha', r'\\beta', r'\\gamma', r'\\delta', r'\\epsilon', r'\\zeta', r'\\eta', r'\\theta', r'\\iota', r'\\kappa',
        r'\\lambda', r'\\mu', r'\\nu', r'\\xi', r'\\omicron', r'\\pi', r'\\rho', r'\\sigma', r'\\tau', r'\\upsilon',
        r'\\phi', r'\\chi', r'\\psi', r'\\omega',
        r'\\varepsilon', r'\\vartheta', r'\\varpi', r'\\varrho', r'\\varsigma', r'\\varphi', r'\\varkappa',
        r'\\partial', r'\\nabla', r'\\infty', r'\\emptyset', r'\\varnothing',
        r'\\aleph', r'\\beth', r'\\gimel', r'\\daleth',
        r'\\hbar', r'\\ell', r'\\wp', r'\\Re', r'\\Im', r'\\mho', r'\\prime', r'\\backprime',
        r'\\leftarrow', r'\\rightarrow', r'\\uparrow', r'\\downarrow', r'\\leftrightarrow', r'\\updownarrow',
        r'\\Leftarrow', r'\\Rightarrow', r'\\Uparrow', r'\\Downarrow', r'\\Leftrightarrow', r'\\Updownarrow',
        r'\\mapsto', r'\\longmapsto', r'\\hookleftarrow', r'\\hookrightarrow',
        r'\\leq', r'\\geq', r'\\neq', r'\\equiv', r'\\approx', r'\\sim', r'\\simeq', r'\\cong', r'\\propto',
        r'\\prec', r'\\succ', r'\\preceq', r'\\succeq', r'\\subset', r'\\supset', r'\\subseteq', r'\\supseteq',
        r'\\cup', r'\\cap', r'\\setminus', r'\\triangle', r'\\bigtriangleup', r'\\bigtriangledown',
        r'\\land', r'\\lor', r'\\lnot', r'\\forall', r'\\exists', r'\\nexists',
        r'\\cdot', r'\\times', r'\\div', r'\\pm', r'\\mp', r'\\ast', r'\\star', r'\\circ', r'\\bullet',
        r'\\oplus', r'\\ominus', r'\\otimes', r'\\oslash', r'\\odot', r'\\bigcirc'
    ]

    # 按长度降序排序，确保长命令优先匹配
    sorted_latex_commands = sorted(latex_commands_needing_separation, key=len, reverse=True)
    for cmd in sorted_latex_commands:
        escaped_cmd = re.escape(cmd)
        pattern = f'(?<![a-zA-Z\\\\])({escaped_cmd})([a-zA-Z])(?![a-zA-Z]*[{{\\\\])'
        result = re.sub(pattern, r'\1 \2', result)

    # 特殊修复：针对间距命令的直接模式匹配（举一反三的修复）
    spacing_command_patterns = [
        (r'(\\quad)([a-zA-Z])', r'\1 \2'),
        (r'(\\qquad)([a-zA-Z])', r'\1 \2'),
        (r'(\\enspace)([a-zA-Z])', r'\1 \2'),
        (r'(\\thinspace)([a-zA-Z])', r'\1 \2'),
        (r'(\\medspace)([a-zA-Z])', r'\1 \2'),
        (r'(\\thickspace)([a-zA-Z])', r'\1 \2'),
        (r'(\\negthinspace)([a-zA-Z])', r'\1 \2'),
        (r'(\\negmedspace)([a-zA-Z])', r'\1 \2'),
        (r'(\\negthickspace)([a-zA-Z])', r'\1 \2'),
    ]

    for pattern, replacement in spacing_command_patterns:
        result = re.sub(pattern, replacement, result)

    # === 最后阶段：清理空格和恢复换行符 ===

    # 清理连续空格
    result = re.sub(r'\s{2,}', ' ', result)
    result = re.sub(r'^\s+|\s+$', '', result)

    # 恢复被保护的LaTeX换行符
    result = result.replace('__LATEX_NEWLINE__', '\\\\')

    # 最后恢复被保护的LaTeX强制空格命令
    for i, forced_space in enumerate(latex_forced_spaces):
        result = result.replace(f'__LATEX_FORCED_SPACE_{i}__', forced_space)



    return result

def unified_math_function_processing(text):
    """
    统一的数学函数处理 - 保持\sin等函数的原始格式
    只处理明确的数学函数，不影响其他\mathrm用途
    """
    result = text

    # 处理括号补全后的\operatorname格式（第一个字符有花括号，后面的字符用空格分隔）
    operatorname_patterns = [
        (r'\\operatorname\s*\{\s*s\s*\}\s*i\s*n(?=\s|$)', r'\\sin'),
        (r'\\operatorname\s*\{\s*c\s*\}\s*o\s*s(?=\s|$)', r'\\cos'),
        (r'\\operatorname\s*\{\s*t\s*\}\s*a\s*n(?=\s|$)', r'\\tan'),
        (r'\\operatorname\s*\{\s*l\s*\}\s*o\s*g(?=\s|$)', r'\\log'),
        (r'\\operatorname\s*\{\s*e\s*\}\s*x\s*p(?=\s|$)', r'\\exp'),
        (r'\\operatorname\s*\{\s*l\s*\}\s*n(?=\s|$)', r'\\ln'),
        (r'\\operatorname\s*\{\s*l\s*\}\s*g(?=\s|$)', r'\\lg'),
        (r'\\operatorname\s*\{\s*s\s*\}\s*e\s*c(?=\s|$)', r'\\sec'),
        (r'\\operatorname\s*\{\s*c\s*\}\s*s\s*c(?=\s|$)', r'\\csc'),
        (r'\\operatorname\s*\{\s*c\s*\}\s*o\s*t(?=\s|$)', r'\\cot'),
        (r'\\operatorname\s*\{\s*s\s*\}\s*i\s*n\s*h(?=\s|$)', r'\\sinh'),
        (r'\\operatorname\s*\{\s*c\s*\}\s*o\s*s\s*h(?=\s|$)', r'\\cosh'),
        (r'\\operatorname\s*\{\s*t\s*\}\s*a\s*n\s*h(?=\s|$)', r'\\tanh'),
        (r'\\operatorname\s*\{\s*a\s*\}\s*r\s*c\s*s\s*i\s*n(?=\s|$)', r'\\arcsin'),
        (r'\\operatorname\s*\{\s*a\s*\}\s*r\s*c\s*c\s*o\s*s(?=\s|$)', r'\\arccos'),
        (r'\\operatorname\s*\{\s*a\s*\}\s*r\s*c\s*t\s*a\s*n(?=\s|$)', r'\\arctan'),
        (r'\\operatorname\s*\{\s*m\s*\}\s*i\s*n(?=\s|$)', r'\\min'),
        (r'\\operatorname\s*\{\s*m\s*\}\s*a\s*x(?=\s|$)', r'\\max'),
        (r'\\operatorname\s*\{\s*m\s*\}\s*o\s*d(?=\s|$)', r'\\mod'),
    ]

    for pattern, replacement in operatorname_patterns:
        result = re.sub(pattern, replacement, result)

    # 处理括号补全后的\mathrm格式（第一个字符有花括号，后面的字符用空格分隔）
    mathrm_patterns = [
        (r'\\mathrm\s*\{\s*s\s*\}\s*i\s*n(?=\s|$)', r'\\sin'),
        (r'\\mathrm\s*\{\s*c\s*\}\s*o\s*s(?=\s|$)', r'\\cos'),
        (r'\\mathrm\s*\{\s*t\s*\}\s*a\s*n(?=\s|$)', r'\\tan'),
        (r'\\mathrm\s*\{\s*l\s*\}\s*o\s*g(?=\s|$)', r'\\log'),
        (r'\\mathrm\s*\{\s*e\s*\}\s*x\s*p(?=\s|$)', r'\\exp'),
        (r'\\mathrm\s*\{\s*l\s*\}\s*n(?=\s|$)', r'\\ln'),
        (r'\\mathrm\s*\{\s*l\s*\}\s*g(?=\s|$)', r'\\lg'),
        (r'\\mathrm\s*\{\s*s\s*\}\s*e\s*c(?=\s|$)', r'\\sec'),
        (r'\\mathrm\s*\{\s*c\s*\}\s*s\s*c(?=\s|$)', r'\\csc'),
        (r'\\mathrm\s*\{\s*c\s*\}\s*o\s*t(?=\s|$)', r'\\cot'),
        (r'\\mathrm\s*\{\s*s\s*\}\s*i\s*n\s*h(?=\s|$)', r'\\sinh'),
        (r'\\mathrm\s*\{\s*c\s*\}\s*o\s*s\s*h(?=\s|$)', r'\\cosh'),
        (r'\\mathrm\s*\{\s*t\s*\}\s*a\s*n\s*h(?=\s|$)', r'\\tanh'),
        (r'\\mathrm\s*\{\s*a\s*\}\s*r\s*c\s*s\s*i\s*n(?=\s|$)', r'\\arcsin'),
        (r'\\mathrm\s*\{\s*a\s*\}\s*r\s*c\s*c\s*o\s*s(?=\s|$)', r'\\arccos'),
        (r'\\mathrm\s*\{\s*a\s*\}\s*r\s*c\s*t\s*a\s*n(?=\s|$)', r'\\arctan'),
        (r'\\mathrm\s*\{\s*m\s*\}\s*i\s*n(?=\s|$)', r'\\min'),
        (r'\\mathrm\s*\{\s*m\s*\}\s*a\s*x(?=\s|$)', r'\\max'),
        (r'\\mathrm\s*\{\s*m\s*\}\s*o\s*d(?=\s|$)', r'\\mod'),
    ]

    for pattern, replacement in mathrm_patterns:
        result = re.sub(pattern, replacement, result)

    return result

def normalize_latex(l, rm_trail=False):
    """
    LaTeX规范化主函数 - 重新设计流程版本
    新的处理流程：空格处理 → Token合并 → 其他处理
    """
    # 移除了对\text{}命令的特殊保护，按用户要求不再特殊处理

    # 步骤1: 尾部清理 (可选)
    if rm_trail:
        l = remove_trailing_latex(l)

    # 步骤2: 基础矩阵命令替换
    l = l.strip().replace(r'\pmatrix', r'\mypmatrix').replace(r'\matrix', r'\mymatrix')

    # 步骤3: 移除对齐相关命令
    for item in ['\\raggedright', '\\arraybackslash']:
        l = l.replace(item, "")

    # 步骤4: 移除大小写转换命令
    for item in ['\\lowercase', '\\uppercase']:
        l = l.replace(item, "")

    # 步骤5: 统一空格处理 (提前到Token合并之前)
    l = unified_space_processing(l)
    


    # 步骤6: 使用通用函数处理Token合并 (在空格处理之后)
    # 处理空格命令
    l = merge_tokens_with_pattern(l, r'\\[hv]space { [.0-9a-z ]+ }')

    # 处理 \begin{array} 格式
    def process_array_format(token):
        return token.replace("\\begin{array} ", "<s>").replace(" ", "").replace("<s>", "\\begin{array} ")
    l = merge_tokens_with_pattern(l, r'\\begin{array} { [lrc ]+ }', process_array_format)

    # Token合并处理
    l = merge_tokens_with_pattern(l, r'\\string [^ ]+ ', add_trailing_space=True)
    l = merge_tokens_with_pattern(l, r'\\[Bb]ig[g]?[glrm]? [(){}|\[\]] ', add_trailing_space=True)
    
    # 处理Big命令与后面LaTeX命令的合并（现在在空格处理之后，不会被覆盖）
    def process_big_command(token):
        # 移除Big命令和后面LaTeX命令之间的空格
        return token.replace(" ", "")
    l = merge_tokens_with_pattern(l, r'\\[Bb]ig[g]?[glrm]?\s*\\[a-zA-Z]+', process_big_command, add_trailing_space=True)
    
    def process_operatorname_star(token):
        return "\\operatorname"
    l = merge_tokens_with_pattern(l, r'\\operatorname \*', process_operatorname_star)

    # 移除有害命令
    l = l.replace("\\lefteqn", "")
    l = l.replace("\\footnote ", "^ ")

    # 重音符号合并
    l = merge_tokens_with_pattern(l, r'\\\' [^{] ', add_trailing_space=True)

    # 其他命令合并
    l = merge_tokens_with_pattern(l, r'\\parbox {[^{]+}')
    
    def process_raisebox(token):
        processed = token.replace(" ", "")
        return processed[0:-1] + " {"
    l = merge_tokens_with_pattern(l, r'\\raisebox {[^{]+} [\[\]0-9 exptcm]+{', process_raisebox)
    
    def process_char_command(token):
        processed = token.replace(" ", "")
        return "{ " + processed[1:-1] + " }"
    l = merge_tokens_with_pattern(l, r'{ \\char[0-9\' ]+}', process_char_command)
    
    l = merge_tokens_with_pattern(l, r'\\rule {[ .0-9a-z]+} {[ .0-9a-z]+}')
    l = merge_tokens_with_pattern(l, r'\\specialrule {[ .0-9a-z]+} {[ .0-9a-z]+} {[ .0-9a-z]+}')

    # 步骤7: 颜色命令移除
    pattern = r'\\colorbox[ \[\]RGBrgb]+{ [A-Za-z 0-9,!]+ } |\\color[ \[\]RGBrgb]+{ [A-Za-z 0-9,!]+ } |\\textcolor[ \[\]RGBrgb]+{ [A-Za-z 0-9,!]+ } |\\cellcolor[ \[\]RGBrgb]+{ [A-Za-z 0-9,!]+ } '
    old_token = re.findall(pattern, l, re.DOTALL)
    for bef in old_token:
        l = l.replace(bef, "")

    # 步骤8: 括号补全处理
    l_split = latex_aware_tokenizer(l)
    idx = 0

    while idx < len(l_split):
        token = l_split[idx]
        if token in ONE_Tail_Tokens + ONE_Tail_Invisb_Tokens:
            if idx + 1 < len(l_split):
                next_token = l_split[idx + 1]
                if next_token != "{":
                    l_split.insert(idx + 1, "{")
                    l_split.insert(idx + 3, "}")
                    idx += 2
        elif token in TWO_Tail_Tokens:
            if idx + 1 < len(l_split):
                current_pos = idx + 1
                if l_split[current_pos].startswith("{") and l_split[current_pos].endswith("}"):
                    current_pos += 1
                elif l_split[current_pos] != "{":
                    l_split.insert(current_pos, "{")
                    l_split.insert(current_pos + 2, "}")
                    current_pos += 3
                else:
                    brace_count = 1
                    current_pos += 1
                    while current_pos < len(l_split) and brace_count > 0:
                        if l_split[current_pos] == "{":
                            brace_count += 1
                        elif l_split[current_pos] == "}":
                            brace_count -= 1
                        current_pos += 1

                if current_pos < len(l_split):
                    if l_split[current_pos].startswith("{") and l_split[current_pos].endswith("}"):
                        current_pos += 1
                    elif l_split[current_pos] != "{":
                        l_split.insert(current_pos, "{")
                        l_split.insert(current_pos + 2, "}")
                        current_pos += 3
                    else:
                        brace_count = 1
                        current_pos += 1
                        while current_pos < len(l_split) and brace_count > 0:
                            if l_split[current_pos] == "{":
                                brace_count += 1
                            elif l_split[current_pos] == "}":
                                brace_count -= 1
                            current_pos += 1

                idx = current_pos - 1
        elif token in TWO_Tail_Invisb_Tokens:
            if idx + 1 < len(l_split):
                if l_split[idx + 1] != "{":
                    l_split.insert(idx + 1, "{")
                    l_split.insert(idx + 3, "}")
                    idx += 2
                if idx + 3 < len(l_split) and l_split[idx + 3] != "{":
                    l_split.insert(idx + 3, "{")
                    l_split.insert(idx + 5, "}")
                    idx += 2
        elif token in AB_Tail_Tokens:
            if token == "\\sqrt":
                if idx + 1 < len(l_split):
                    if l_split[idx + 1] == "[":
                        bracket_end = find_matching_brace(l_split, idx + 1, brace=['[', ']'])
                        if bracket_end != -1:
                            idx = bracket_end
                    if idx + 1 < len(l_split) and l_split[idx + 1] != "{":
                        l_split.insert(idx + 1, "{")
                        l_split.insert(idx + 3, "}")
                        idx += 2
            else:
                if idx + 1 < len(l_split):
                    if l_split[idx + 1] == "[":
                        bracket_end = find_matching_brace(l_split, idx + 1, brace=['[', ']'])
                        if bracket_end != -1:
                            idx = bracket_end
                    if idx + 1 < len(l_split) and l_split[idx + 1] != "{":
                        l_split.insert(idx + 1, "{")
                        l_split.insert(idx + 3, "}")
                        idx += 2
        idx += 1

    l = ' '.join(l_split)

    # 步骤9: 统一数学函数处理（在括号补全之后）
    l = unified_math_function_processing(l)
    


    # 步骤10: 连字符展开和省略号统一化
    l = re.sub(r'---', r'- - -', l)
    l = re.sub(r'--', r'- -', l)

    # 省略号统一化
    # 修复：先处理所有dots变体，避免被错误拆分
    l = re.sub(r'\\dotsc', r'. . .', l)  # 先处理dotsc，避免被错误拆分
    l = re.sub(r'\\dotsi', r'. . .', l)  # 先处理dotsi，避免被错误拆分
    l = re.sub(r'\\dotsm', r'. . .', l)  # 先处理dotsm，避免被错误拆分
    l = re.sub(r'\\dotso', r'. . .', l)  # 先处理dotso，避免被错误拆分
    l = re.sub(r'\\dotsb', r'. . .', l)  # 先处理dotsb，避免被错误拆分
    
    l = re.sub(r'…', r'. . .', l)
    l = re.sub(r'\\ldots', r'. . .', l)
    l = re.sub(r'\\hdots', r'. . .', l)
    l = re.sub(r'\\cdots', r'. . .', l)
    l = re.sub(r'\\dddot', r'. . .', l)
    l = re.sub(r'\\dots', r'. . .', l)
    l = re.sub(r'\\mathellipsis', r'. . .', l)

    # 步骤11: 删除空花括号和多余的嵌套花括号
    l = remove_empty_braces(l)
    l = remove_redundant_braces(l)

    # 步骤12: 修复align环境中的花括号问题
    l = fix_align_environment_braces(l)

    # 移除了\text{}块的恢复逻辑
    


    return l.strip()

def tokenize_latex_string(latex_string):
    """
    LaTeX tokenize流程 (从原脚本复制)

    Args:
        latex_string: 原始LaTeX字符串

    Returns:
        tokenize后的LaTeX字符串
    """
    if not latex_string or not latex_string.strip():
        return latex_string

    try:
        # JavaScript AST处理
        success, tokenized_latex = tokenize_latex(latex_string)
        if not success:
            # JavaScript处理失败时，返回原始字符串
            return latex_string

        return tokenized_latex

    except Exception as e:
        print(f"tokenize处理出错: {e}")
        return latex_string

def replace_synonym_tokens(tokenized_string):
    """
    同义词token替换函数 (从原脚本复制)

    Args:
        tokenized_string: tokenize后的LaTeX字符串

    Returns:
        替换同义词后的LaTeX字符串
    """
    if not tokenized_string or not tokenized_string.strip():
        return tokenized_string

    # 检查是否启用同义词替换功能
    if not CONFIG.get('enable_synonym_replacement', True):
        return tokenized_string

    # 配置文件路径
    csv_file_path = SCRIPT_DIR / "token_unify.csv"

    # 如果配置文件不存在，跳过替换步骤
    if not csv_file_path.exists():
        return tokenized_string

    try:
        # 读取CSV配置文件
        synonym_map = {}
        with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
            csv_reader = csv.reader(csvfile)
            for row_num, row in enumerate(csv_reader, 1):
                # 跳过空行
                if not row or len(row) < 2:
                    continue

                # 第一列是同义词，第二列是标准token
                synonym = row[0].strip()
                standard_token = row[1].strip()

                # 跳过空的条目
                if not synonym or not standard_token:
                    continue

                synonym_map[synonym] = standard_token

        if not synonym_map:
            return tokenized_string

        # 执行token替换
        result = tokenized_string

        # 按同义词长度降序排序，避免短token被长token的一部分误匹配
        sorted_synonyms = sorted(synonym_map.keys(), key=len, reverse=True)

        replacement_count = 0
        for synonym in sorted_synonyms:
            standard_token = synonym_map[synonym]

            # 简单的字符串替换，确保匹配完整的token
            if synonym in result:
                new_result = result.replace(synonym, standard_token)

                if new_result != result:
                    replacement_count += 1
                    result = new_result



        return result

    except Exception as e:
        return tokenized_string

def normalize_latex_string(latex_string):
    """
    完整的LaTeX规范化流程 (优化版本)

    Args:
        latex_string: 原始LaTeX字符串

    Returns:
        规范化后的LaTeX字符串
    """
    if not latex_string or not latex_string.strip():
        return latex_string

    try:
        # 第一阶段: JavaScript AST处理 (保持不变)
        success, tokenized_latex = tokenize_latex(latex_string)
        if not success:
            # JavaScript处理失败时，直接使用Python规则处理
            tokenized_latex = latex_string

        # 第二阶段: Python规则处理 (优化版本)
        normalized_latex = normalize_latex(tokenized_latex, CONFIG.get('remove_trailing', False))

        return normalized_latex

    except Exception as e:
        print(f"规范化处理出错: {e}")
        return latex_string

# ==================== 配置验证函数 ====================

def validate_config():
    """
    验证配置参数的有效性 (从原脚本复制)
    """
    errors = []

    # 检查必需的参数
    if 'mode' not in CONFIG:
        errors.append("缺少必需参数: 'mode'")
    elif CONFIG['mode'] not in ['tokenize', 'normalize']:
        errors.append(f"无效的mode值: '{CONFIG['mode']}'，应为: 'tokenize', 'normalize'")

    # 检查LaTeX字符串参数
    if 'latex_string' not in CONFIG or not CONFIG['latex_string']:
        errors.append("需要设置 'latex_string' 参数")

    # 输出验证结果
    if errors:
        print("❌ 配置错误:")
        for error in errors:
            print(f"  - {error}")
        return False

    return True



def process_latex():
    """处理LaTeX字符串"""
    latex_string = CONFIG['latex_string']
    mode = CONFIG['mode']

    if mode == 'tokenize':
        # tokenize模式: tokenize → 同义词替换 → 输出
        tokenized_result = tokenize_latex_string(latex_string)
        result = replace_synonym_tokens(tokenized_result)
    elif mode == 'normalize':
        # normalize模式: tokenize → 同义词替换 → normalize → 输出
        # 第一阶段: JavaScript AST处理
        success, tokenized_latex = tokenize_latex(latex_string)
        if not success:
            tokenized_latex = latex_string

        # 第二阶段: 同义词替换
        synonym_replaced_latex = replace_synonym_tokens(tokenized_latex)

        # 第三阶段: Python规则处理
        result = normalize_latex(synonym_replaced_latex, CONFIG.get('remove_trailing', False))
    else:
        print(f"未知的模式: {mode}")
        return latex_string

    # 输出结果
    print("=" * 80)
    print("LaTeX 规范化处理结果")
    print("=" * 80)
    print(f"输入: {latex_string}")
    print(f"输出: {result}")
    print("=" * 80)

    return result

# ==================== 主函数 ====================



def main():
    """
    主函数 - 简化输出版本
    """
    # 验证配置
    if not validate_config():
        print("配置错误，请修正后重新运行")
        return

    # 处理LaTeX字符串
    process_latex()

if __name__ == "__main__":
    main()
