#!/usr/bin/env python3
"""
调试测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from normalize_optimized import tokenize_latex

def debug_tokenize():
    """调试tokenize过程"""
    test_cases = [
        r'\sin x',
        r'\mathrm{sin} x',
        r'\operatorname{sin} x',
    ]
    
    for test_case in test_cases:
        print(f"输入: {test_case}")
        success, result = tokenize_latex(test_case)
        print(f"JavaScript输出: {result}")
        print()

if __name__ == "__main__":
    debug_tokenize()
